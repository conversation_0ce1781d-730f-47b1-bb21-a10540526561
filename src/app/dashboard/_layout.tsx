import { MobileMenuControlProvider, useMobileMenuControl } from '@/contexts/MobileMenuContext';
import { Outlet, useNavigate } from 'react-router';
import { cn } from '@/lib/utils';
import { AppHeader } from '@/components/header/header';
import { DashboardSidebar } from '@/components/dashboard/sidebar/sidebar';
import { SidebarProvider, useSidebarContext } from '@/components/dashboard/sidebar/sideBarContext';
import { useAuth } from '@/hooks/auth';

interface DashboardLayoutProps {
  className?: string;
}

function DashboardLayoutContent({ className }: DashboardLayoutProps) {
  const { activeMobileMenu, toggleMobileMenu, closeMobileMenu } = useMobileMenuControl();
  const { sidebarCollapsed } = useSidebarContext();
  const { user, signOut } = useAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  const getUserInitials = () => {
    if (user?.user_metadata?.full_name) {
      return user.user_metadata.full_name
        .split(' ')
        .map(name => name[0])
        .join('')
        .toUpperCase();
    }

    if (user?.email) {
      return user.email.substring(0, 2).toUpperCase();
    }
  };

  return (
    <div className={cn('dashboard-layout min-h-screen w-full bg-background', className)}>
      {/* Fixed Header */}
      <AppHeader
        title="HBN AuPair"
        showMobileMenuToggle={true}
        onMobileMenuToggle={() => toggleMobileMenu('main')}
        showSearch={true}
        showNotifications={true}
        showUserProfile={true}
        user={{
          name: user?.user_metadata?.full_name || user?.email || 'User',
          initials: getUserInitials(),
          avatarUrl: user?.user_metadata?.avatar_url,
        }}
        onSignOut={handleSignOut}
      />

      {/* Fixed Sidebar */}
      <DashboardSidebar isMobileMenuOpen={activeMobileMenu === 'main'} closeMobileMenu={closeMobileMenu} />

      {/* Main Content Area with proper spacing for fixed header and sidebar */}
      <div className={cn(
        "dashboard-main-wrapper pt-16 transition-all duration-300 ease-in-out",
        "pl-0", // No left padding on mobile
        sidebarCollapsed ? "md:pl-16" : "md:pl-64"
      )}>
        <main className="flex-1 overflow-auto p-4 md:p-6 min-h-screen">
          <Outlet />
        </main>
      </div>
    </div>
  );
}

export default function DashboardLayout(props: DashboardLayoutProps) {
  return (
    <SidebarProvider>
      <MobileMenuControlProvider>
        <DashboardLayoutContent {...props} />
      </MobileMenuControlProvider>
    </SidebarProvider>
  );
}

interface DashboardLayoutProps {
  className?: string;
}
