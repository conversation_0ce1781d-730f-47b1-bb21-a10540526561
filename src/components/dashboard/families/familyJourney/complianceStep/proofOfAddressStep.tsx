import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, Upload } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface ProofOfAddressStepProps {
  onSubmit?: () => void;
  isCompleted?: boolean;
}

export default function ProofOfAddressStep({ onSubmit, isCompleted = false }: ProofOfAddressStepProps) {
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Proof of address submitted');
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-gray-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <Clock className={`h-6 w-6 ${isCompleted ? 'text-black' : 'text-gray-600'}`} />
              <span className="text-lg font-semibold">3.1 Proof of Address</span>
              <Badge className={isCompleted ? 'bg-black text-white' : 'bg-gray-600 text-white'}>
                {isCompleted ? 'Completed' : 'In Progress'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="md:col-span-2 space-y-6">
                    <div>
                      <h4 className="font-bold text-black text-xl mb-3">LAW:</h4>
                      <p className="text-gray-700 text-lg leading-relaxed">
                        Under the Dutch Modern Migration Policy Act, recognized au pair agencies are legally required to
                        keep a BRP extract (official population register document) in their records.
                      </p>
                    </div>

                    <div>
                      <h4 className="font-bold text-black text-xl mb-3">To-Do:</h4>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 text-lg">
                        <li>Request an official BRP extract from your local City Hall</li>
                        <li>Ensure that the BRP shows at least two people registered at the address</li>
                        <li>Make sure the BRP extract is not older than six months</li>
                      </ul>
                    </div>
                  </div>

                  <div className="bg-black text-white p-8 rounded-lg">
                    <h3 className="text-xl font-bold mb-6">BRP Extract</h3>
                    <p className="text-sm mb-6 text-gray-300">Upload maximum 1 file</p>
                    <div className="border-2 border-dashed border-white/30 rounded-lg p-8 text-center">
                      <Upload className="h-10 w-10 mx-auto mb-3 text-white/70" />
                      <p className="text-sm text-white/90">Drag and drop files or click to browse</p>
                    </div>
                    <Button
                      className="w-full mt-6 bg-white text-black hover:bg-gray-100"
                      onClick={handleSubmit}
                      disabled={isCompleted}
                    >
                      PROCEED
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
