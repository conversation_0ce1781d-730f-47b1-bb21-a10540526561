import { useState, useEffect } from 'react';
import FamilyInformationStep from './intakeStep/familyInformationStep';
import AssignmentConfirmationStep from './intakeStep/assignmentConfirmationStep';
import IntakeMeetingStep from './intakeStep/intakeMeetingStep';

interface Child {
  name: string;
  age: string;
  gender: string;
}

interface FamilyFormData {
  familyName: string;
  address: string;
  postalCode: string;
  city: string;
  country: string;
  phone: string;
  email: string;
  children: Child[];
  preferredStartDate: string;
  latestStartDate: string;
}

interface IntakeStepProps {
  currentStep?: number;
}

export default function IntakeStep({ currentStep = 0 }: IntakeStepProps) {
  const [familyData, setFamilyData] = useState<FamilyFormData>({
    familyName: '',
    address: '',
    postalCode: '',
    city: '',
    country: '',
    phone: '',
    email: '',
    children: [],
    preferredStartDate: '',
    latestStartDate: '',
  });

  const [familyInfoCompleted, setFamilyInfoCompleted] = useState(false);
  const [assignmentCompleted, setAssignmentCompleted] = useState(true);
  const [intakeMeetingCompleted, setIntakeMeetingCompleted] = useState(true);

  const handleFamilyInfoSubmit = (data: FamilyFormData) => {
    setFamilyData(data);
    setFamilyInfoCompleted(true);
    console.log('Family information submitted:', data);
  };

  const handleAssignmentConfirmation = () => {
    setAssignmentCompleted(true);
    console.log('Assignment confirmation completed');
  };

  const handleIntakeMeetingConfirmation = () => {
    setIntakeMeetingCompleted(true);
    console.log('Intake meeting completed');
  };

  // Scroll to the current step when currentStep changes
  useEffect(() => {
    const stepElement = document.getElementById(`step-${currentStep}`);
    if (stepElement) {
      stepElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [currentStep]);

  return (
    <div className="space-y-8">
      <div id="step-0" className={currentStep === 0 ? 'scroll-mt-4' : ''}>
        <FamilyInformationStep
          formData={familyData}
          setFormData={setFamilyData}
          onSubmit={handleFamilyInfoSubmit}
          isCompleted={familyInfoCompleted}
          isExpanded={currentStep === 0}
        />
      </div>

      <div id="step-1" className={currentStep === 1 ? 'scroll-mt-4' : ''}>
        <AssignmentConfirmationStep
          onSubmit={handleAssignmentConfirmation}
          isCompleted={assignmentCompleted}
          isExpanded={currentStep === 1}
        />
      </div>

      <div id="step-2" className={currentStep === 2 ? 'scroll-mt-4' : ''}>
        <IntakeMeetingStep
          onSubmit={handleIntakeMeetingConfirmation}
          isCompleted={intakeMeetingCompleted}
          isExpanded={currentStep === 2}
        />
      </div>
    </div>
  );
}
