import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface CandidateReviewStepProps {
  onSubmit?: () => void;
  isCompleted?: boolean;
}

export default function CandidateReviewStep({ onSubmit, isCompleted = false }: CandidateReviewStepProps) {
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Candidate review step submitted');
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-gray-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <Users className={`h-6 w-6 ${isCompleted ? 'text-black' : 'text-gray-600'}`} />
              <span className="text-lg font-semibold">4.2 Candidate Review</span>
              <Badge className={isCompleted ? 'bg-black text-white' : 'bg-gray-600 text-white'}>
                {isCompleted ? 'Completed' : 'Pending'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="text-center py-12">
                  <Users className="h-20 w-20 text-gray-400 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">Review Potential Matches</h3>
                  <p className="text-gray-600 mb-6 text-lg max-w-md mx-auto">
                    Once matching begins, you'll be able to review and connect with potential au pair candidates.
                  </p>
                  <Badge variant="secondary" className="bg-gray-200 text-gray-700 px-4 py-2 text-lg">
                    Available After Matching Begins
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
