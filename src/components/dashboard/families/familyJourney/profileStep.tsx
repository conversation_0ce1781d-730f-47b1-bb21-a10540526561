import { useState, useEffect } from 'react';
import DearAuPairLetterStep from './profileStep/dearAuPairLetterStep';
import FamilyPicturesStep from './profileStep/familyPicturesStep';
import WeeklyScheduleStep from './profileStep/weeklyScheduleStep';

interface ProfileStepProps {
  currentStep?: number;
}

export default function ProfileStep({ currentStep = 0 }: ProfileStepProps) {
  const [letterContent, setLetterContent] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [letterCompleted, setLetterCompleted] = useState(false);
  const [picturesCompleted, setPicturesCompleted] = useState(false);
  const [scheduleCompleted, setScheduleCompleted] = useState(false);

  const handleLetterSubmit = (content: string) => {
    setLetterContent(content);
    setLetterCompleted(true);
    console.log('Dear Au-Pair letter submitted:', content);
  };

  const handlePicturesSubmit = (files: File[]) => {
    setUploadedFiles(files);
    setPicturesCompleted(true);
    console.log('Family pictures submitted:', files);
  };

  const handleScheduleSubmit = () => {
    setScheduleCompleted(true);
    console.log('Weekly schedule submitted');
  };

  // Scroll to the current step when currentStep changes
  useEffect(() => {
    const stepElement = document.getElementById(`step-${currentStep}`);
    if (stepElement) {
      stepElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [currentStep]);

  return (
    <div className="space-y-8">
      <div id="step-0" className={currentStep === 0 ? 'scroll-mt-4' : ''}>
        <DearAuPairLetterStep
          onSubmit={handleLetterSubmit}
          initialContent={letterContent}
          isCompleted={letterCompleted}
        />
      </div>

      <div id="step-1" className={currentStep === 1 ? 'scroll-mt-4' : ''}>
        <FamilyPicturesStep
          onSubmit={handlePicturesSubmit}
          uploadedFiles={uploadedFiles}
          isCompleted={picturesCompleted}
        />
      </div>

      <div id="step-2" className={currentStep === 2 ? 'scroll-mt-4' : ''}>
        <WeeklyScheduleStep onSubmit={handleScheduleSubmit} isCompleted={scheduleCompleted} />
      </div>
    </div>
  );
}
