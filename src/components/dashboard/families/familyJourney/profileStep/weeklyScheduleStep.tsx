import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface WeeklyScheduleProps {
  onSubmit?: () => void;
  isCompleted?: boolean;
}

export default function WeeklyScheduleStep({ onSubmit, isCompleted = false }: WeeklyScheduleProps) {
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Weekly schedule submitted');
  };

  const handleOpenScheduleBuilder = () => {
    // This would open the schedule builder modal or navigate to schedule builder page
    console.log('Opening schedule builder...');
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-gray-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <Clock className={`h-6 w-6 ${isCompleted ? 'text-black' : 'text-gray-600'}`} />
              <span className="text-lg font-semibold">2.3 Weekly Time Schedule</span>
              <Badge className={isCompleted ? 'bg-black text-white' : 'bg-gray-600 text-white'}>
                {isCompleted ? 'Completed' : 'In Progress'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="space-y-6">
                  <div className="bg-gray-50 p-6 rounded-lg border-2 border-gray-200">
                    <h4 className="font-bold text-black text-xl mb-3">LAW:</h4>
                    <p className="text-gray-700 text-lg leading-relaxed">
                      You are legally required to create a weekly schedule for your Au Pair. It must be approved, dated
                      and signed by both Host Parents and the Au Pair before we may send in the visa application.
                    </p>
                  </div>

                  <div>
                    <h4 className="font-bold text-black text-xl mb-3">LEGAL BOUNDARIES:</h4>
                    <ul className="list-disc list-inside space-y-2 text-gray-700 text-lg">
                      <li>Light household chores and childcare only</li>
                      <li>Max: 30 hours/week, 8 hours/day</li>
                      <li>At least 2 days off/week</li>
                      <li>1 full weekend off/month (Fri 20:00 – Sun 23:00)</li>
                      <li>Babysitting (even if kids are asleep) counts as working hours</li>
                    </ul>
                  </div>

                  <div className="bg-gray-100 p-8 rounded-lg border-2 border-gray-200">
                    <h4 className="font-bold text-black text-xl mb-4 text-center">Schedule Builder</h4>
                    <p className="text-gray-600 mb-6 text-center text-lg">
                      Use our interactive schedule builder to create your weekly routine.
                    </p>
                    <div className="space-y-4">
                      <Button
                        className="w-full bg-black text-white hover:bg-gray-800"
                        onClick={handleOpenScheduleBuilder}
                      >
                        Open Schedule Builder
                      </Button>

                      {isCompleted && (
                        <div className="text-center">
                          <p className="text-green-600 font-semibold mb-4">✓ Schedule has been created and approved</p>
                          <Button
                            variant="outline"
                            className="w-full border-black text-black hover:bg-black hover:text-white"
                            onClick={handleSubmit}
                          >
                            View/Edit Schedule
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
