import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Check, ChevronDown, Clock, ChevronLeft, ChevronRight, X, CheckCircle2, Circle } from 'lucide-react';
import { cn } from '@/lib/utils';
import React, { useState, useMemo } from 'react';

interface Step {
  id: string;
  title: string;
  description: string;
  type: string;
  icon: React.ElementType;
}

interface Stage {
  id: number;
  title: string;
  description: string;
  icon: React.ElementType;
  steps: Step[];
}

interface SidebarContentProps {
  stages: Stage[];
  currentStage: number;
  currentStep: number;
  expandedStages: number[];
  stepStatus: { [key: string]: boolean };
  toggleStageExpansion: (stageId: number) => void;
  goToStep: (stageId: number, stepIndex: number) => void;
  isMobileMenuOpen?: boolean;
  className?: string;
  onCollapsedChange?: (collapsed: boolean) => void;
  initialCollapsed?: boolean;
  closeMobileMenu?: () => void;
}

const SidebarContent: React.FC<SidebarContentProps> = ({
  stages,
  currentStage,
  currentStep,
  expandedStages,
  stepStatus,
  toggleStageExpansion,
  goToStep,
  isMobileMenuOpen = false,
  className,
  onCollapsedChange,
  initialCollapsed = false,
  closeMobileMenu, // Destructure the new prop
}) => {
  // Use local state for each sidebar's collapsed state instead of global context
  const [sidebarCollapsed, setSidebarCollapsed] = useState(initialCollapsed);

  // Calculate overall progress
  const progressData = useMemo(() => {
    const allSteps = stages.flatMap(stage => stage.steps);
    const completedSteps = allSteps.filter(step => stepStatus[step.id]);
    const totalSteps = allSteps.length;
    const completedCount = completedSteps.length;
    const progressPercentage = totalSteps > 0 ? Math.round((completedCount / totalSteps) * 100) : 0;

    return {
      totalSteps,
      completedCount,
      progressPercentage,
      currentStageProgress:
        stages.find(s => s.id === currentStage)?.steps.filter(step => stepStatus[step.id]).length || 0,
      currentStageTotal: stages.find(s => s.id === currentStage)?.steps.length || 0,
    };
  }, [stages, stepStatus, currentStage]);

  // Notify parent component when collapse state changes
  const toggleSidebar = () => {
    const newState = !sidebarCollapsed;
    setSidebarCollapsed(newState);
    if (onCollapsedChange) {
      onCollapsedChange(newState);
    }
  };

  // Function to handle mobile sidebar backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    // Prevent event propagation
    e.stopPropagation();

    // Only handle if mobile menu is open
    if (isMobileMenuOpen) {
      if (closeMobileMenu) {
        closeMobileMenu();
      } else if (onCollapsedChange) {
        onCollapsedChange(true); // Fallback to existing behavior
      }
    }
  };

  // Enhanced navigation function for stage clicks
  const handleStageClick = (stageId: number) => {
    if (sidebarCollapsed) {
      // In collapsed state: navigate to stage, expand sidebar, and expand stage
      goToStep(stageId, 0); // Navigate to first step of the stage
      setSidebarCollapsed(false); // Expand the sidebar
      if (onCollapsedChange) {
        onCollapsedChange(false);
      }
      // Expand the clicked stage after a brief delay to allow sidebar expansion animation
      setTimeout(() => {
        if (!expandedStages.includes(stageId)) {
          toggleStageExpansion(stageId);
        }
      }, 150);
    } else {
      // In expanded state: navigate to stage and toggle expansion
      goToStep(stageId, 0); // Navigate to first step of the stage
      toggleStageExpansion(stageId); // Toggle stage expansion
    }
  };

  return (
    <TooltipProvider>
      <aside className={cn('h-full w-full flex flex-col bg-background', className)}>
        {/* Mobile close button - only visible on mobile when sidebar is open */}
        {isMobileMenuOpen && (
          <div className="flex justify-between items-center p-3 border-b border-border lg:hidden">
            <h3 className="font-semibold text-foreground">Family Journey</h3>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={closeMobileMenu || handleBackdropClick}
              aria-label="Close sidebar"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Desktop collapse/expand button - only visible on desktop */}
        {!isMobileMenuOpen && (
          <button
            className={cn(
              'absolute top-4 -left-3 z-30 bg-background border border-border/60 rounded-full p-1.5 shadow-md',
              'transition-all duration-200 hover:shadow-lg hidden lg:flex lg:items-center lg:justify-center',
              'transform hover:scale-105 active:scale-95'
            )}
            style={{ left: '-16px' }}
            onClick={toggleSidebar}
            aria-label={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {sidebarCollapsed ? <ChevronLeft size={16} /> : <ChevronRight size={16} />}
          </button>
        )}

        {/* Refined Sidebar Header with Progress */}
        {!sidebarCollapsed && !isMobileMenuOpen && (
          <div className="p-4 border-b border-border bg-background">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-base text-foreground">Family Journey</h3>
                <Badge variant="outline" className="text-xs font-medium border-foreground text-foreground">
                  {progressData.progressPercentage}%
                </Badge>
              </div>

              <div className="space-y-3">
                <Progress value={progressData.progressPercentage} className="h-2 bg-muted [&>div]:bg-foreground" />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span className="font-medium">
                    {progressData.completedCount} of {progressData.totalSteps} steps completed
                  </span>
                  <span className="flex items-center gap-1">
                    <CheckCircle2 className="h-3 w-3 text-foreground" />
                    <span className="font-medium text-foreground">{progressData.completedCount}</span>
                  </span>
                </div>
              </div>

              {/* Current Stage Progress */}
              <div className="pt-3 border-t border-border">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground font-medium">Current Stage Progress</span>
                  <span className="font-semibold text-foreground">
                    {progressData.currentStageProgress}/{progressData.currentStageTotal}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Collapsed State Progress Indicator */}
        {sidebarCollapsed && !isMobileMenuOpen && (
          <div className="p-3 border-b border-border">
            <div className="flex flex-col items-center space-y-3">
              <div className="relative">
                <Circle className="h-10 w-10 text-muted-foreground stroke-1" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs font-bold text-foreground">{progressData.progressPercentage}</span>
                </div>
              </div>
              <div className="w-full bg-muted rounded-full h-1.5">
                <div
                  className="bg-foreground h-1.5 rounded-full transition-all duration-300"
                  style={{ width: `${progressData.progressPercentage}%` }}
                />
              </div>
            </div>
          </div>
        )}

        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div
              className={cn(
                'px-2 py-3 sm:px-3 lg:px-4 space-y-2 sm:space-y-3',
                sidebarCollapsed ? 'px-1.5' : '',
                isMobileMenuOpen ? 'pb-20' : ''
              )}
            >
              {stages.map(stage => {
                const isCurrentStage = stage.id === currentStage;
                const isExpanded = expandedStages.includes(stage.id);
                const StageIcon = stage.icon;
                const stageCompleted = stage.steps.every(step => stepStatus[step.id]);

                // Calculate stage progress
                const stageProgress = stage.steps.filter(step => stepStatus[step.id]).length;
                const stageTotal = stage.steps.length;
                const stageProgressPercentage = stageTotal > 0 ? Math.round((stageProgress / stageTotal) * 100) : 0;

                return (
                  <div key={stage.id} className="w-full group">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant={isCurrentStage ? 'secondary' : 'ghost'}
                          className={cn(
                            'w-full h-auto py-3 px-3 text-left relative rounded-lg transition-all duration-200',
                            'border border-transparent hover:border-border',
                            isCurrentStage ? 'bg-muted shadow-sm border-border' : 'hover:bg-muted/50',
                            stageCompleted ? 'bg-muted/80 border-border text-foreground' : '',
                            sidebarCollapsed
                              ? 'justify-center px-2 py-2 hover:bg-muted hover:shadow-md hover:scale-105 active:scale-95 cursor-pointer'
                              : '',
                            'group-hover:shadow-sm'
                          )}
                          onClick={() => handleStageClick(stage.id)}
                          aria-label={
                            sidebarCollapsed
                              ? `Navigate to ${stage.title} and expand sidebar`
                              : `Navigate to ${stage.title}`
                          }
                        >
                          <div
                            className={cn('flex items-center gap-3 min-w-0 w-full', !sidebarCollapsed ? 'pr-8' : '')}
                          >
                            {/* Refined Stage Icon with Status */}
                            <div className="relative">
                              <div
                                className={cn(
                                  'w-8 h-8 lg:w-9 lg:h-9 rounded-lg flex items-center justify-center flex-shrink-0 transition-all duration-200',
                                  'border',
                                  stageCompleted
                                    ? 'bg-foreground text-background border-foreground'
                                    : isCurrentStage
                                      ? 'bg-muted text-foreground border-foreground'
                                      : 'bg-background text-muted-foreground border-border group-hover:bg-muted/50 group-hover:text-foreground',
                                  sidebarCollapsed && 'group-hover:border-foreground/50 group-hover:shadow-sm'
                                )}
                              >
                                {stageCompleted ? (
                                  <CheckCircle2 className="h-4 w-4 lg:h-5 lg:w-5" />
                                ) : (
                                  <StageIcon className="h-4 w-4 lg:h-5 lg:w-5" />
                                )}
                              </div>

                              {/* Current Stage Indicator */}
                              {isCurrentStage && !stageCompleted && !sidebarCollapsed && (
                                <div className="absolute -inset-1">
                                  <div className="w-full h-full rounded-lg border-2 border-foreground/30 animate-pulse" />
                                </div>
                              )}
                            </div>

                            {!sidebarCollapsed && (
                              <div className="min-w-0 flex-1 space-y-1">
                                <div className="flex items-center justify-between">
                                  <div className="font-semibold text-sm lg:text-base truncate">{stage.title}</div>
                                  {stageCompleted && (
                                    <Badge variant="outline" className="text-xs border-foreground text-foreground">
                                      Complete
                                    </Badge>
                                  )}
                                </div>

                                <div className="text-xs text-muted-foreground truncate">{stage.description}</div>

                                {/* Stage Progress Bar */}
                                <div className="flex items-center gap-2 pt-1">
                                  <div className="flex-1 bg-muted rounded-full h-1.5">
                                    <div
                                      className="h-1.5 rounded-full transition-all duration-300 bg-foreground"
                                      style={{ width: `${stageProgressPercentage}%` }}
                                    />
                                  </div>
                                  <span className="text-xs font-semibold text-foreground min-w-fit">
                                    {stageProgress}/{stageTotal}
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Expand/Collapse Indicator */}
                          {!sidebarCollapsed && (
                            <div className="absolute right-3 top-1/2 -translate-y-1/2">
                              <ChevronDown
                                className={cn(
                                  'h-4 w-4 transition-all duration-200 text-muted-foreground',
                                  'group-hover:text-foreground',
                                  isExpanded ? 'rotate-180' : ''
                                )}
                              />
                            </div>
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="left" className="max-w-xs hidden lg:block">
                        <div className="space-y-2">
                          <div>
                            <div className="font-semibold">{stage.title}</div>
                            <div className="text-xs text-muted-foreground">{stage.description}</div>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span>Progress:</span>
                            <span className="font-medium">
                              {stageProgressPercentage}% ({stageProgress}/{stageTotal})
                            </span>
                          </div>
                          {sidebarCollapsed && (
                            <div className="text-xs text-muted-foreground border-t pt-2">
                              Click to navigate and expand
                            </div>
                          )}
                        </div>
                      </TooltipContent>
                    </Tooltip>

                    {isExpanded && !sidebarCollapsed && (
                      <div className="ml-4 lg:ml-6 pl-3 lg:pl-4 border-l-2 border-border mt-3 mb-4 space-y-2 mr-2 lg:mr-3">
                        {stage.steps.map((step, stepIndex) => {
                          const StepIcon = step.icon;
                          const isCurrentStep = isCurrentStage && stepIndex === currentStep;
                          const isStepCompleted = stepStatus[step.id];

                          return (
                            <Tooltip key={step.id}>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className={cn(
                                    'w-full justify-start h-auto py-2 px-1.5 sm:py-2.5 sm:px-2.5 lg:px-3 text-left rounded-md transition-all duration-200',
                                    'border border-transparent hover:border-border',
                                    'max-w-full overflow-visible cursor-pointer',
                                    isCurrentStep
                                      ? 'bg-muted shadow-sm border-border'
                                      : 'hover:bg-muted/50 hover:shadow-sm',
                                    isStepCompleted ? 'bg-muted/80 border-border' : '',
                                    'group/step hover:shadow-sm hover:scale-[1.02] active:scale-[0.98]',
                                    'pr-1.5 sm:pr-2',
                                    'mx-0.5 sm:mx-0' // Add horizontal margin on mobile
                                  )}
                                  aria-label={`Navigate to ${step.title}`}
                                  onClick={() => goToStep(stage.id, stepIndex)}
                                >
                                  <div className="flex items-start sm:items-center gap-2 lg:gap-3 w-full min-w-0">
                                    {/* Refined Step Icon */}
                                    <div className="relative flex-shrink-0 mt-0.5 sm:mt-0">
                                      <div
                                        className={cn(
                                          'w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 rounded-md flex items-center justify-center transition-all duration-200',
                                          'border',
                                          isStepCompleted
                                            ? 'bg-foreground text-background border-foreground'
                                            : isCurrentStep
                                              ? 'bg-muted text-foreground border-foreground'
                                              : 'bg-background text-muted-foreground border-border group-hover/step:bg-muted/50 group-hover/step:text-foreground'
                                        )}
                                      >
                                        {isStepCompleted ? (
                                          <Check className="h-2.5 w-2.5 sm:h-3 sm:w-3 lg:h-3.5 lg:w-3.5" />
                                        ) : (
                                          <StepIcon className="h-2.5 w-2.5 sm:h-3 sm:w-3 lg:h-3.5 lg:w-3.5" />
                                        )}
                                      </div>

                                      {/* Current Step Indicator - Adjusted for better visibility */}
                                      {isCurrentStep && !isStepCompleted && (
                                        <div className="absolute -inset-0.5">
                                          <div className="w-full h-full rounded-md border-2 border-foreground/50 animate-pulse" />
                                        </div>
                                      )}
                                    </div>

                                    <div className="min-w-0 flex-1 pr-0.5 sm:pr-0 -my-0.5">
                                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-0.5 sm:gap-2">
                                        <div
                                          className={cn(
                                            'text-sm font-medium whitespace-normal break-words text-left leading-tight',
                                            isStepCompleted
                                              ? 'text-foreground'
                                              : isCurrentStep
                                                ? 'text-foreground'
                                                : 'text-foreground/80'
                                          )}
                                        >
                                          {step.title}
                                        </div>

                                        {/* Step Status Indicators - Optimized for space */}
                                        <div className="flex items-center gap-1 flex-shrink-0">
                                          {isStepCompleted && (
                                            <Badge
                                              variant="outline"
                                              className="text-[10px] leading-none py-0.5 h-5 border-foreground text-foreground px-1.5"
                                            >
                                              Done
                                            </Badge>
                                          )}
                                          {isCurrentStep && !isStepCompleted && (
                                            <div className="flex items-center gap-1">
                                              <Clock className="h-3 w-3 text-foreground animate-pulse flex-shrink-0" />
                                              <Badge
                                                variant="outline"
                                                className="text-[10px] leading-none py-0.5 h-5 border-foreground text-foreground px-1.5"
                                              >
                                                Active
                                              </Badge>
                                            </div>
                                          )}
                                        </div>
                                      </div>

                                      <div className="text-xs text-muted-foreground whitespace-normal break-words text-left mt-1 leading-snug pr-1">
                                        {step.description}
                                      </div>
                                    </div>
                                  </div>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent side="left" className="max-w-xs hidden lg:block">
                                <div className="space-y-2">
                                  <div>
                                    <div className="font-semibold">{step.title}</div>
                                    <div className="text-xs text-muted-foreground">{step.description}</div>
                                  </div>
                                  <div className="flex items-center gap-2 text-xs">
                                    <span
                                      className={cn(
                                        'px-2 py-1 rounded-md font-medium border',
                                        isStepCompleted
                                          ? 'bg-foreground text-background border-foreground'
                                          : isCurrentStep
                                            ? 'bg-muted text-foreground border-foreground'
                                            : 'bg-background text-muted-foreground border-border'
                                      )}
                                    >
                                      {isStepCompleted ? 'Completed' : isCurrentStep ? 'In Progress' : 'Pending'}
                                    </span>
                                  </div>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </div>
      </aside>
    </TooltipProvider>
  );
};

export default SidebarContent;
