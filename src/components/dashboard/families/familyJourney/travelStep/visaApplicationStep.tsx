import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { FileCheck } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface VisaApplicationStepProps {
  onSubmit?: () => void;
  isCompleted?: boolean;
}

export default function VisaApplicationStep({ onSubmit, isCompleted = false }: VisaApplicationStepProps) {
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Visa application step submitted');
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-gray-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <FileCheck className={`h-6 w-6 ${isCompleted ? 'text-black' : 'text-gray-600'}`} />
              <span className="text-lg font-semibold">5.1 Visa Application</span>
              <Badge className={isCompleted ? 'bg-black text-white' : 'bg-gray-600 text-white'}>
                {isCompleted ? 'Completed' : 'Pending Previous Steps'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="text-center py-12">
                  <FileCheck className="h-20 w-20 text-gray-400 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">Visa Application Process</h3>
                  <p className="text-gray-600 mb-6 text-lg max-w-md mx-auto">
                    This step will become available once you have completed the matching process and selected your au
                    pair.
                  </p>
                  <Button
                    className="bg-black text-white hover:bg-gray-800"
                    onClick={handleSubmit}
                    disabled={!isCompleted}
                  >
                    BEGIN VISA APPLICATION
                  </Button>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
