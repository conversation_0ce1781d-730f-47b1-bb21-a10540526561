import { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MoreHorizontal, Search, ArrowUpDown } from 'lucide-react';
import { Link } from 'react-router';
import { Badge } from '@/components/ui/badge';

// Mock data for families
const families = [
  {
    id: '1',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+49 123 456789',
    city: 'Munich',
    country: 'Germany',
    children: 2,
    startDate: '2023-09-01',
    status: 'searching',
  },
  {
    id: '2',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+44 7700 900123',
    city: 'London',
    country: 'United Kingdom',
    children: 1,
    startDate: '2023-08-15',
    status: 'matched',
  },
  {
    id: '3',
    firstName: 'Antonio',
    lastName: 'Garcia',
    email: '<EMAIL>',
    phone: '+34 987 654321',
    city: 'Barcelona',
    country: 'Spain',
    children: 3,
    startDate: '2023-10-01',
    status: 'searching',
  },
  {
    id: '4',
    firstName: 'Marie',
    lastName: 'Dubois',
    email: '<EMAIL>',
    phone: '+33 6 12 34 56 78',
    city: 'Paris',
    country: 'France',
    children: 2,
    startDate: '2023-09-15',
    status: 'matched',
  },
  {
    id: '5',
    firstName: 'Marco',
    lastName: 'Rossi',
    email: '<EMAIL>',
    phone: '+39 333 1234567',
    city: 'Milan',
    country: 'Italy',
    children: 1,
    startDate: '2023-11-01',
    status: 'inactive',
  },
];

export function FamilyTable() {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState('lastName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const filteredFamilies = families
    .filter(
      family =>
        family.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        family.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        family.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        family.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
        family.country.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      const aValue = a[sortColumn as keyof typeof a];
      const bValue = b[sortColumn as keyof typeof b];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }

      return 0;
    });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'searching':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Searching</Badge>;
      case 'matched':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Matched</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Inactive</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search families..."
            className="pl-8"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[250px]">
                <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort('lastName')}>
                  Name
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" className="p-0 font-medium">
                  Contact Details
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort('country')}>
                  Location
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort('children')}>
                  Children
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort('startDate')}>
                  Start Date
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[70px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredFamilies.map(family => (
              <TableRow key={family.id}>
                <TableCell>
                  <Link to={`/dashboard/families/${family.id}`} className="font-medium hover:underline">
                    {family.firstName} {family.lastName}
                  </Link>
                </TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <span>{family.email}</span>
                    <span className="text-muted-foreground text-sm">{family.phone}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {family.city}, {family.country}
                </TableCell>
                <TableCell>{family.children}</TableCell>
                <TableCell>{new Date(family.startDate).toLocaleDateString()}</TableCell>
                <TableCell>{getStatusBadge(family.status)}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Link to={`/dashboard/families/${family.id}`} className="flex w-full">
                          View details
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Link to={`/dashboard/families/${family.id}/edit`} className="flex w-full">
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
