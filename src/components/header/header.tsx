import type { ReactNode } from 'react';
import { <PERSON> } from 'react-router';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { BellIcon, MenuIcon, SearchIcon, UserIcon } from 'lucide-react';

interface AppHeaderProps {
  title?: string;
  logo?: ReactNode;
  showMobileMenuToggle?: boolean;
  onMobileMenuToggle?: () => void;
  showSearch?: boolean;
  onSearchClick?: () => void;
  showNotifications?: boolean;
  onNotificationsClick?: () => void;
  showUserProfile?: boolean;
  user?: {
    name: string;
    email?: string;
    avatarUrl?: string;
    initials?: string;
  };
  userMenuItems?: ReactNode;
  actions?: ReactNode;
  className?: string;
  onSignOut?: () => void;
}

export function AppHeader({
  title,
  logo,
  showMobileMenuToggle = false,
  onMobileMenuToggle,
  showSearch = false,
  onSearchClick,
  showNotifications = false,
  onNotificationsClick,
  showUserProfile = false,
  user = {
    name: 'User',
    initials: 'U',
  },
  userMenuItems,
  actions,
  onSignOut,
}: AppHeaderProps) {
  return (
    <div>
      <header className="fixed top-0 z-50 flex h-16 w-full items-center border-b bg-background/95 backdrop-blur-sm px-4 md:px-6">
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center gap-2">
            {showMobileMenuToggle && (
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                onClick={onMobileMenuToggle}
                aria-label="Toggle menu"
              >
                <MenuIcon className="h-5 w-5" />
              </Button>
            )}

            {logo ? (
              <div className="flex items-center gap-2">
                {logo}
                {title && <span className="text-xl font-semibold">{title}</span>}
              </div>
            ) : (
              title && <h1 className="text-xl font-semibold">{title}</h1>
            )}
          </div>

          <div className="flex items-center gap-2">
            {actions}

            {showSearch && (
              <Button variant="ghost" size="icon" onClick={onSearchClick} aria-label="Search">
                <SearchIcon className="h-5 w-5" />
              </Button>
            )}

            {showNotifications && (
              <Button variant="ghost" size="icon" onClick={onNotificationsClick} aria-label="Notifications">
                <BellIcon className="h-5 w-5" />
              </Button>
            )}

            {showUserProfile && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="rounded-full">
                    <Avatar className="h-8 w-8">
                      {user.avatarUrl ? (
                        <AvatarImage src={user.avatarUrl} alt={user.name} />
                      ) : (
                        <AvatarFallback>{user.initials || <UserIcon className="h-4 w-4" />}</AvatarFallback>
                      )}
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{user.name}</p>
                      {user.email && <p className="text-xs leading-none text-muted-foreground">{user.email}</p>}
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {userMenuItems || (
                    <>
                      <DropdownMenuItem asChild>
                        <Link to="/dashboard/settings">Settings</Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={onSignOut}>Sign Out</DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </header>
    </div>
  );
}
