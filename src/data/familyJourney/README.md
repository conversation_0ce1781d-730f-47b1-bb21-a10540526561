# Family Journey Configuration System

This directory contains the configuration files for the Family Journey feature, making it easy to manage journey steps, UI settings, and behavior without modifying React components.

## Files Overview

### `familyJourneyStages.json`

The main configuration file that defines all journey stages and steps.

### `familyJourneyConfig.json`

UI and behavior configuration for the journey system.

### `familyJourneyStages.ts`

TypeScript interfaces and helper functions for the configuration system.

## Configuration Structure

### Journey Stages (`familyJourneyStages.json`)

Each stage in the journey has the following structure:

```json
{
  "id": 1,
  "title": "Stage Title",
  "description": "Stage description",
  "icon": "IconName",
  "componentName": "ComponentName",
  "borderColor": "border-l-color",
  "badgeColor": "bg-color",
  "steps": [...]
}
```

**Properties:**

- `id`: Unique identifier for the stage
- `title`: Display name of the stage
- `description`: Brief description shown in the UI
- `icon`: Icon name from Lucide React icons
- `componentName`: Name of the React component to render for this stage
- `borderColor`: Tailwind CSS class for the stage border color
- `badgeColor`: Tailwind CSS class for the stage badge color
- `steps`: Array of step objects

### Journey Steps

Each step within a stage has this structure:

```json
{
  "id": "step-id",
  "title": "Step Title",
  "description": "Step description",
  "type": "step-type",
  "icon": "IconName",
  "defaultStatus": false
}
```

**Properties:**

- `id`: Unique identifier for the step
- `title`: Display name of the step
- `description`: Brief description of what the step involves
- `type`: Type of step (agreement, schedule, upload, form, etc.)
- `icon`: Icon name from Lucide React icons
- `defaultStatus`: Whether the step should be marked as complete by default

### UI Configuration (`familyJourneyConfig.json`)

The configuration file contains several sections:

#### Navigation Settings

```json
"navigation": {
  "showPreviousButton": true,
  "showNextButton": true,
  "showStepCounter": true,
  "previousButtonText": "Previous",
  "nextButtonText": "Continue",
  "doneButtonText": "Done"
}
```

#### Sidebar Settings

```json
"sidebar": {
  "defaultCollapsed": false,
  "showProgress": true,
  "showStageProgress": true,
  "enableMobileSwipe": true,
  "progressLabels": {
    "completed": "completed",
    "currentStage": "Current Stage",
    "journeyProgress": "Journey Progress"
  }
}
```

#### Stage Settings

```json
"stages": {
  "defaultExpanded": [1],
  "allowMultipleExpanded": true,
  "showCompletionBadges": true,
  "completionBadgeText": "Complete",
  "activeBadgeText": "Active",
  "pendingBadgeText": "Pending"
}
```

#### Step Settings

```json
"steps": {
  "autoAdvanceDelay": 500,
  "showStatusIcons": true,
  "showDescriptions": true,
  "completionAnimation": true
}
```

#### Behavior Settings

```json
"behavior": {
  "autoExpandNextStage": true,
  "autoCollapseCompletedStages": false,
  "persistProgress": true,
  "allowSkipSteps": false,
  "requireSequentialCompletion": true
}
```

## How to Make Changes

### Adding a New Journey Step

1. Open `familyJourneyStages.json`
2. Find the appropriate stage
3. Add a new step object to the `steps` array:

```json
{
  "id": "new-step-id",
  "title": "New Step Title",
  "description": "Description of the new step",
  "type": "form",
  "icon": "FileText",
  "defaultStatus": false
}
```

### Removing a Journey Step

1. Open `familyJourneyStages.json`
2. Find the step you want to remove
3. Delete the step object from the `steps` array

### Reordering Steps

1. Open `familyJourneyStages.json`
2. Cut and paste step objects within the `steps` array to reorder them

### Adding a New Stage

1. Open `familyJourneyStages.json`
2. Add a new stage object to the main array
3. Create a corresponding React component
4. Update the component mapping in the main page component

### Modifying UI Behavior

1. Open `familyJourneyConfig.json`
2. Update the relevant configuration values
3. The changes will be automatically applied

## Available Icons

The system uses Lucide React icons. Available icons include:

- `User`, `FileCheck`, `Video`, `Users`, `Heart`
- `FileText`, `CalendarIcon2`, `Shield`, `DollarSign`
- `MapPin`, `Home`, `CheckCircle`, `GraduationCap`

To add new icons:

1. Import them in `familyJourneyStages.ts`
2. Add them to the `iconMap` object
3. Use the icon name in your JSON configuration

## Best Practices

1. **Consistent Naming**: Use kebab-case for step IDs
2. **Meaningful Descriptions**: Provide clear, concise descriptions
3. **Logical Ordering**: Arrange steps in the order they should be completed
4. **Default Status**: Set `defaultStatus: true` only for steps that should be pre-completed
5. **Color Consistency**: Use consistent color schemes for related stages
6. **Component Mapping**: Ensure all stages have corresponding React components

## Troubleshooting

- **Missing Icons**: Check that the icon name exists in the `iconMap`
- **Component Not Found**: Verify the component name matches exactly
- **Configuration Not Applied**: Ensure JSON syntax is valid
- **TypeScript Errors**: Run type checking to catch configuration mismatches
