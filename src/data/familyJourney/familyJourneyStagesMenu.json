[{"id": 1, "title": "Intake & Confirmation", "description": "Welcome and initial setup", "icon": "User", "componentName": "IntakeStep", "borderColor": "border-l-black", "badgeColor": "bg-black", "steps": [{"id": "family-information", "title": "Family Information", "description": "Provide family details and contact information", "type": "form", "icon": "Users", "defaultStatus": false}, {"id": "assignment-confirmation", "title": "Assignment Confirmation", "description": "Review and confirm assignment", "type": "agreement", "icon": "FileCheck", "defaultStatus": false}, {"id": "intake-meeting", "title": "Intake Meeting & Guidelines", "description": "Video call and guidelines review", "type": "schedule", "icon": "Video", "defaultStatus": false}]}, {"id": 2, "title": "Profile Setup", "description": "Family profile & preferences", "icon": "Users", "componentName": "ProfileStep", "borderColor": "border-l-black", "badgeColor": "bg-black", "steps": [{"id": "dear-aupair-letter", "title": "Dear Au-Pair Letter", "description": "Write a personal letter to your future au pair", "type": "text", "icon": "Heart", "defaultStatus": false}, {"id": "family-pictures", "title": "Family Pictures", "description": "Upload family photos", "type": "upload", "icon": "FileText", "defaultStatus": false}, {"id": "weekly-schedule", "title": "Weekly Time Schedule", "description": "Create work schedule", "type": "schedule", "icon": "CalendarIcon2", "defaultStatus": false}]}, {"id": 3, "title": "Compliance Check", "description": "Legal & income verification", "icon": "Shield", "componentName": "ComplianceStep", "borderColor": "border-l-gray-600", "badgeColor": "bg-gray-600", "steps": [{"id": "proof-address", "title": "Proof of Address", "description": "BRP extract upload", "type": "upload", "icon": "FileText", "defaultStatus": false}, {"id": "legal-residence", "title": "Legal Residence", "description": "Passport and BSN details", "type": "form", "icon": "FileCheck", "defaultStatus": false}, {"id": "income-review", "title": "Income Review", "description": "Employment and salary documentation", "type": "upload", "icon": "DollarSign", "defaultStatus": false}]}, {"id": 4, "title": "Matching Process", "description": "Find your perfect Au Pair", "icon": "Heart", "componentName": "MatchingStep", "borderColor": "border-l-gray-400", "badgeColor": "bg-gray-400", "steps": [{"id": "matching-start", "title": "Begin Matching", "description": "Start the matching process", "type": "display", "icon": "Heart", "defaultStatus": false}, {"id": "candidate-review", "title": "Candidate Review", "description": "Review potential matches", "type": "form", "icon": "Users", "defaultStatus": false}]}, {"id": 5, "title": "Travel & Visa", "description": "Visa application and travel arrangements", "icon": "MapPin", "componentName": "TravelStep", "borderColor": "border-l-gray-400", "badgeColor": "bg-gray-400", "steps": [{"id": "visa-application", "title": "Visa Application", "description": "Submit visa application documents", "type": "form", "icon": "FileCheck", "defaultStatus": false}, {"id": "travel-arrangements", "title": "Travel Arrangements", "description": "Book flights and arrange travel", "type": "form", "icon": "MapPin", "defaultStatus": false}]}, {"id": 6, "title": "Pre-Arrival", "description": "Preparation for Au Pair arrival", "icon": "Home", "componentName": "PreArrivalStep", "borderColor": "border-l-blue-500", "badgeColor": "bg-blue-500", "steps": [{"id": "room-preparation", "title": "Room Preparation", "description": "Prepare Au Pair accommodation", "type": "checklist", "icon": "Home", "defaultStatus": false}, {"id": "orientation-materials", "title": "Orientation Materials", "description": "Download preparation guides", "type": "download", "icon": "FileText", "defaultStatus": false}]}, {"id": 7, "title": "Arrival", "description": "Au Pair arrival and initial setup", "icon": "MapPin", "componentName": "ArrivalStep", "borderColor": "border-l-green-500", "badgeColor": "bg-green-500", "steps": [{"id": "airport-pickup", "title": "Airport Pickup", "description": "Confirm arrival pickup", "type": "confirm", "icon": "MapPin", "defaultStatus": false}, {"id": "first-week", "title": "First Week Setup", "description": "Initial orientation tasks", "type": "checklist", "icon": "CheckCircle", "defaultStatus": false}]}, {"id": 8, "title": "Program Period", "description": "During the Au Pair program", "icon": "Users", "componentName": "ProgramStep", "borderColor": "border-l-purple-500", "badgeColor": "bg-purple-500", "steps": [{"id": "monthly-checkins", "title": "Monthly Check-ins", "description": "Regular program evaluations", "type": "form", "icon": "Heart", "defaultStatus": false}, {"id": "support-resources", "title": "Support Resources", "description": "Access help and guidance", "type": "info", "icon": "GraduationCap", "defaultStatus": false}]}, {"id": 9, "title": "Program Completion", "description": "End of program procedures", "icon": "CheckCircle", "componentName": "DepartureStep", "borderColor": "border-l-red-500", "badgeColor": "bg-red-500", "steps": [{"id": "final-evaluation", "title": "Final Evaluation", "description": "Program completion assessment", "type": "form", "icon": "FileCheck", "defaultStatus": false}, {"id": "departure-checklist", "title": "Departure Checklist", "description": "Final departure tasks", "type": "checklist", "icon": "CheckCircle", "defaultStatus": false}]}]